
/** @type {import('next').NextConfig} */
const nextConfig = {
    output: "standalone", // for self-hosting
    eslint: {
        // 빌드 시 ESLint 오류를 무시 (개발 중에는 IDE에서 확인)
        ignoreDuringBuilds: true,
    },
    pageExtensions: ['js', 'jsx', 'mdx', 'ts', 'tsx'],

    // 실험적 기능 - 더 나은 브라우저 호환성
    experimental: {
        // 더 나은 번들링
        optimizePackageImports: ['lucide-react', '@radix-ui/react-icons']
    },

    // 헤더 설정 (프록시 환경에서 필요할 수 있음)
    // async headers() {
    //     return [
    //         {
    //             source: '/(.*)',
    //             headers: [
    //                 {
    //                     key: 'X-Frame-Options',
    //                     value: 'SAMEORIGIN',
    //                 },
    //                 {
    //                     key: 'X-Content-Type-Options',
    //                     value: 'nosniff',
    //                 },
    //             ],
    //         },
    //     ];
    // },

};

export default nextConfig

