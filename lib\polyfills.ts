// Chrome 86 지원을 위한 polyfills
// @ts-ignore
import structuredClone from '@ungap/structured-clone';

// structuredClone polyfill for Chrome < 98
if (typeof globalThis.structuredClone === 'undefined') {
  (globalThis as any).structuredClone = structuredClone;
}

// ResizeObserver polyfill for Chrome < 64 (Chrome 86에서는 지원되지만 안전장치)
if (typeof window !== 'undefined' && !window.ResizeObserver) {
  // ResizeObserver polyfill 로드
  import('resize-observer-polyfill').then((module) => {
    (window as any).ResizeObserver = module.default;
  }).catch(() => {
    // polyfill 로드 실패 시 더미 구현
    (window as any).ResizeObserver = class {
      observe() {}
      unobserve() {}
      disconnect() {}
    };
  });
}

// IntersectionObserver polyfill for Chrome < 51 (Chrome 86에서는 지원되지만 안전장치)
if (typeof window !== 'undefined' && !window.IntersectionObserver) {
  // IntersectionObserver polyfill 로드
  import('intersection-observer').then(() => {
    // polyfill이 자동으로 전역에 추가됨
  }).catch(() => {
    // polyfill 로드 실패 시 더미 구현
    (window as any).IntersectionObserver = class {
      constructor(_callback: any) {}
      observe() {}
      unobserve() {}
      disconnect() {}
    };
  });
}

// CSS.supports polyfill for Chrome < 61 (Chrome 86에서는 지원되지만 안전장치)
if (typeof window !== 'undefined' && typeof CSS !== 'undefined' && !CSS.supports) {
  CSS.supports = function() {
    return false; // 안전한 fallback
  };
}

// 기타 필요한 polyfills
import 'core-js/stable';
