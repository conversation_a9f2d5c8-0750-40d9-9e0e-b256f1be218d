@tailwind base;
@tailwind components;
@tailwind utilities;

  @layer base {
    :root {
      --background: 0 0% 100%;
      --foreground: 222.2 84% 4.9%;

      --card: 0 0% 100%;
      --card-foreground: 222.2 84% 4.9%;

      --popover: 0 0% 100%;
      --popover-foreground: 222.2 84% 4.9%;

      --primary: 222.2 47.4% 11.2%;
      --primary-foreground: 210 40% 98%;

      --secondary: 210 40% 96.1%;
      --secondary-foreground: 222.2 47.4% 11.2%;

      --muted: 210 40% 96.1%;
      --muted-foreground: 215.4 16.3% 46.9%;

      --accent: 210 40% 96.1%;
      --accent-foreground: 222.2 47.4% 11.2%;

      --destructive: 0 84.2% 60.2%;
      --destructive-foreground: 210 40% 98%;

      --border: 214.3 31.8% 91.4%;
      --input: 214.3 31.8% 91.4%;
      --ring: 222.2 84% 4.9%;

      --radius: 0.5rem;

      --sidebar-background: 0 0% 98%;

      --sidebar-foreground: 240 5.3% 26.1%;

      --sidebar-primary: 240 5.9% 10%;

      --sidebar-primary-foreground: 0 0% 98%;

      --sidebar-accent: 240 4.8% 95.9%;

      --sidebar-accent-foreground: 240 5.9% 10%;

      --sidebar-border: 220 13% 91%;

      --sidebar-ring: 217.2 91.2% 59.8%;
    }

    .dark {
      --background: 240 5.9% 10%;
      --foreground: 240 4.8% 95.9%;

      --card: 222.2 84% 4.9%;
      --card-foreground: 210 40% 98%;

      --popover: 222.2 84% 4.9%;
      --popover-foreground: 210 40% 98%;

      --primary: 224.3 76.3% 48%;
      --primary-foreground: 0 0% 100%;

      --secondary: 217.2 32.6% 17.5%;
      --secondary-foreground: 210 40% 98%;

      --muted: 217.2 32.6% 17.5%;
      --muted-foreground: 215 20.2% 65.1%;

      --accent: 240 3.7% 15.9%;
      --accent-foreground: 240 4.8% 95.9%;

      --destructive: 0 62.8% 30.6%;
      --destructive-foreground: 210 40% 98%;

      --border: 240 3.7% 15.9%;
      --input: 217.2 32.6% 17.5%;
      --ring: 217.2 91.2% 59.8%;

      --sidebar-background: 240 5.9% 10%;
      --sidebar-foreground: 240 4.8% 95.9%;

      --sidebar-primary: 224.3 76.3% 48%;
      --sidebar-primary-foreground: 0 0% 100%;

      --sidebar-accent: 240 3.7% 15.9%;
      --sidebar-accent-foreground: 240 4.8% 95.9%;
      --sidebar-border: 240 3.7% 15.9%;
      --sidebar-ring: 217.2 91.2% 59.8%;
    }

  }

  @layer base {
    * {
      @apply border-border;
    }
    body {
      @apply bg-background text-foreground;
    }
  }


.styled-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #d4d4d4 transparent;
  /* scrollbar-color: #eaeaea transparent; */
  /* scrollbar-gutter: stable; Chrome 94+ 전용이므로 주석 처리 */
}

.dark .styled-scrollbar {
  scrollbar-color: #27272a transparent;
}

.ai-textarea-border {
  /* Fallback for older browsers */
  background: #4663A9;
  background: linear-gradient(to right, #4663A9, #557bff, #6991ff, #81a7ff, #9bbbff, #9ac9ff, #a0d5ff, #ace0ff, #9be8ff, #8af0ff, #7df8ff, #79fff4);
}

/* Chrome 86 호환성을 위한 추가 CSS */
@supports not (scrollbar-gutter: stable) {
  .styled-scrollbar {
    /* Chrome 86에서 scrollbar-gutter 대신 padding으로 대체 */
    padding-right: 8px;
  }
}

/* CSS Grid fallback for older browsers */
@supports not (display: grid) {
  .grid {
    display: flex;
    flex-wrap: wrap;
  }
}

/* CSS Variables fallback (Chrome 49+에서 지원되므로 대부분 괜찮지만 안전장치) */
@supports not (--css: variables) {
  :root {
    /* Fallback colors */
    background-color: #ffffff;
    color: #000000;
  }

  .dark {
    background-color: #0a0a0a;
    color: #ffffff;
  }
}

/* Chrome 86 호환성을 위한 추가 CSS 수정 */

/* backdrop-filter fallback (Chrome 76+에서 지원되므로 괜찮지만 안전장치) */
@supports not (backdrop-filter: blur(10px)) {
  .backdrop-blur-sm {
    background-color: rgba(255, 255, 255, 0.9) !important;
  }

  .dark .backdrop-blur-sm {
    background-color: rgba(0, 0, 0, 0.9) !important;
  }
}

/* aspect-ratio fallback (Chrome 88+에서 지원, Chrome 86에서는 미지원) */
@supports not (aspect-ratio: 1) {
  .aspect-square {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 100%;
  }

  .aspect-square > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

/* gap property fallback for flexbox (Chrome 84+에서 지원, Chrome 86에서는 지원) */
/* 하지만 안전장치로 추가 */
@supports not (gap: 1rem) {
  .flex.gap-1 > * + * { margin-left: 0.25rem; }
  .flex.gap-2 > * + * { margin-left: 0.5rem; }
  .flex.gap-3 > * + * { margin-left: 0.75rem; }
  .flex.gap-4 > * + * { margin-left: 1rem; }
  .flex.gap-6 > * + * { margin-left: 1.5rem; }
  .flex.gap-8 > * + * { margin-left: 2rem; }

  .flex.flex-col.gap-1 > * + * { margin-top: 0.25rem; margin-left: 0; }
  .flex.flex-col.gap-2 > * + * { margin-top: 0.5rem; margin-left: 0; }
  .flex.flex-col.gap-3 > * + * { margin-top: 0.75rem; margin-left: 0; }
  .flex.flex-col.gap-4 > * + * { margin-top: 1rem; margin-left: 0; }
  .flex.flex-col.gap-6 > * + * { margin-top: 1.5rem; margin-left: 0; }
  .flex.flex-col.gap-8 > * + * { margin-top: 2rem; margin-left: 0; }
}

/* scroll-behavior fallback - Chrome 61+에서 지원되므로 Chrome 86에서는 괜찮음 */

/* Chrome 86 특정 버그 수정 */

/* Tailwind의 ring 효과가 Chrome 86에서 제대로 작동하지 않을 수 있음 */
.focus\:ring-2:focus {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Chrome 86에서 transform 관련 이슈 수정 */
.transform {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* Chrome 86에서 sticky positioning 이슈 */
.sticky {
  position: -webkit-sticky;
  position: sticky;
}

/* Chrome 86에서 backdrop-filter 대체 */
.backdrop-blur-sm {
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

@supports not (backdrop-filter: blur(4px)) {
  .backdrop-blur-sm {
    background-color: rgba(255, 255, 255, 0.8);
  }

  .dark .backdrop-blur-sm {
    background-color: rgba(0, 0, 0, 0.8);
  }
}

/* Chrome 86에서 text-overflow ellipsis 개선 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

/* Chrome 86에서 flexbox gap 대체 (이미 위에서 처리했지만 추가 보완) */
.space-x-1 > * + * {
  margin-left: 0.25rem;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.space-x-3 > * + * {
  margin-left: 0.75rem;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.space-y-1 > * + * {
  margin-top: 0.25rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

