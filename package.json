{"name": "lx-chatbot", "version": "0.1.0", "private": true, "description": "공유재산 관리 어시스턴트 - AI 기반 공유재산 관리 및 상담 서비스", "keywords": ["공유재산", "관리", "어시스턴트", "AI", "상담", "부동산", "토지"], "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "build:with-db": "tsx lib/db/migrate && next build", "build:ci": "next build", "start": "next start", "lint": "next lint", "migrate": "tsx lib/db/migrate"}, "dependencies": {"@ai-sdk/geon": "0.1.5", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/openai-compatible": "0.2.14", "@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.8", "@ai-sdk/react": "^1.2.12", "@auth/drizzle-adapter": "^1.7.4", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-python": "^6.1.6", "@codemirror/state": "^6.5.0", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.35.3", "@geon-ai/tools": "^0.0.11", "@geon-map/odf": "^0.0.8", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.9.1", "@paralleldrive/cuid2": "^2.2.2", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.3", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.3", "@radix-ui/react-dialog": "^1.1.3", "@radix-ui/react-dropdown-menu": "^2.1.3", "@radix-ui/react-hover-card": "^1.1.3", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.3", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.3", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.5", "@ungap/structured-clone": "^1.3.0", "@vercel/blob": "^0.25.1", "ai": "^4.3.19", "bcrypt-ts": "^5.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "codemirror": "^6.0.1", "core-js": "^3.45.0", "date-fns": "^4.1.0", "debounce": "2.2.0", "dify-ai-provider": "^0.1.6", "dotenv": "^16.4.7", "drizzle-orm": "^0.31.4", "embla-carousel-react": "^8.5.1", "eventsource-parser": "^1.1.2", "fast-deep-equal": "^3.1.3", "framer-motion": "^12.17.3", "geist": "^1.3.1", "intersection-observer": "^0.12.2", "lucide-react": "^0.379.0", "motion": "^12.16.0", "next": "^15.3.3", "next-auth": "5.0.0-beta.26", "next-themes": "^0.3.0", "pg": "^8.13.1", "postgres": "^3.4.5", "prism-react-renderer": "^2.4.1", "react": "^19.0.0", "react-code-block": "^1.1.1", "react-colorful": "^5.6.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.1", "react-markdown": "^9.0.1", "react-resizable-panels": "^2.1.7", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "resize-observer-polyfill": "^1.5.1", "sonner": "^1.7.1", "swr": "^2.2.5", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.0", "vaul": "^0.9.9", "zod": "^3.24.1"}, "devDependencies": {"@types/mdx": "^2.0.13", "@types/node": "^20.17.10", "@types/pg": "^8.11.10", "@types/react": "19.0.1", "@types/react-dom": "^19.0.2", "@types/react-syntax-highlighter": "^15.5.13", "@types/swagger-ui-react": "^4.18.3", "autoprefixer": "^10.4.21", "drizzle-kit": "^0.22.8", "eslint": "^9.17.0", "eslint-config-next": "^15.1.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.16", "tsx": "^4.19.2", "typescript": "^5.7.2"}, "browserslist": {"production": ["Chrome >= 86", "Firefox >= 78", "Safari >= 14", "Edge >= 86"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "packageManager": "pnpm@10.4.1+sha512.c753b6c3ad7afa13af388fa6d808035a008e30ea9993f58c6663e2bc5ff21679aa834db094987129aa4d488b86df57f7b634981b2f827cdcacc698cc0cfb88af"}